#!/bin/bash

# Kubernetes 备份项目本地存储功能测试脚本
# 用于验证本地备份功能的正确性

set -e

# 测试配置
TEST_DIR="/tmp/backup-test-$$"
TEST_NODE_NAME="test-node"
TEST_HOST_IP="127.0.0.1"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试结果统计
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# 测试结果记录
test_result() {
    local test_name="$1"
    local result="$2"
    
    TESTS_TOTAL=$((TESTS_TOTAL + 1))
    
    if [ "$result" = "PASS" ]; then
        TESTS_PASSED=$((TESTS_PASSED + 1))
        log_info "✓ $test_name: PASS"
    else
        TESTS_FAILED=$((TESTS_FAILED + 1))
        log_error "✗ $test_name: FAIL"
    fi
}

# 清理函数
cleanup() {
    log_info "Cleaning up test environment..."
    rm -rf "$TEST_DIR"
}

# 设置测试环境
setup_test_env() {
    log_info "Setting up test environment..."
    
    # 创建测试目录
    mkdir -p "$TEST_DIR"
    
    # 设置环境变量
    export NODE_NAME="$TEST_NODE_NAME"
    export HOST_IP="$TEST_HOST_IP"
    
    # 创建模拟的 etcd 数据和配置文件
    mkdir -p "$TEST_DIR/mock-data/etc/kubernetes"
    mkdir -p "$TEST_DIR/mock-data/var/lib/kubelet/pki"
    
    # 创建模拟文件
    echo "mock etcd data" > "$TEST_DIR/mock-data/snapshot.db"
    echo "mock kubelet config" > "$TEST_DIR/mock-data/var/lib/kubelet/config.yaml"
    echo "mock kubeadm flags" > "$TEST_DIR/mock-data/var/lib/kubelet/kubeadm-flags.env"
    echo "mock kubernetes config" > "$TEST_DIR/mock-data/etc/kubernetes/admin.conf"
    echo "mock pki cert" > "$TEST_DIR/mock-data/var/lib/kubelet/pki/kubelet.crt"
    
    log_info "Test environment setup complete"
}

# 测试 1: 参数解析测试
test_parameter_parsing() {
    log_info "Running parameter parsing test..."
    
    # 创建临时脚本来测试参数解析
    cat > "$TEST_DIR/test-params.sh" << 'EOF'
#!/bin/bash
storageType="s3"
localBackupPath="/backup"
localRetainCount=10

for arg in "$@"; do
    case $arg in
        --storageType=*)
            storageType="${arg#*=}"
            ;;
        --localBackupPath=*)
            localBackupPath="${arg#*=}"
            ;;
        --localRetainCount=*)
            localRetainCount="${arg#*=}"
            ;;
    esac
done

echo "storageType=$storageType"
echo "localBackupPath=$localBackupPath"
echo "localRetainCount=$localRetainCount"
EOF

    chmod +x "$TEST_DIR/test-params.sh"
    
    # 测试参数解析
    local output=$("$TEST_DIR/test-params.sh" \
        --storageType=local \
        --localBackupPath=/test/backup \
        --localRetainCount=5)
    
    if echo "$output" | grep -q "storageType=local" && \
       echo "$output" | grep -q "localBackupPath=/test/backup" && \
       echo "$output" | grep -q "localRetainCount=5"; then
        test_result "Parameter Parsing" "PASS"
    else
        test_result "Parameter Parsing" "FAIL"
        log_error "Expected parameters not found in output: $output"
    fi
}

# 测试 2: 路径验证测试
test_path_validation() {
    log_info "Running path validation test..."
    
    # 创建路径验证函数测试
    cat > "$TEST_DIR/test-path-validation.sh" << 'EOF'
#!/bin/bash
validate_local_path() {
    local path=$1
    
    # 检查路径是否为绝对路径
    if [[ ! "$path" = /* ]]; then
        echo "ERROR: Local backup path must be absolute: $path"
        return 1
    fi
    
    # 检查父目录是否存在
    local parent_dir=$(dirname "$path")
    if [[ ! -d "$parent_dir" ]]; then
        echo "ERROR: Parent directory does not exist: $parent_dir"
        return 1
    fi
    
    return 0
}

# 测试用例
validate_local_path "/tmp/valid-path" && echo "PASS: Valid absolute path"
validate_local_path "relative/path" 2>/dev/null || echo "PASS: Rejected relative path"
validate_local_path "/nonexistent/deep/path" 2>/dev/null || echo "PASS: Rejected nonexistent path"
EOF

    chmod +x "$TEST_DIR/test-path-validation.sh"
    
    local output=$("$TEST_DIR/test-path-validation.sh")
    local pass_count=$(echo "$output" | grep -c "PASS:")
    
    if [ "$pass_count" -eq 3 ]; then
        test_result "Path Validation" "PASS"
    else
        test_result "Path Validation" "FAIL"
        log_error "Path validation test output: $output"
    fi
}

# 测试 3: 目录创建测试
test_directory_creation() {
    log_info "Running directory creation test..."
    
    local test_backup_path="$TEST_DIR/backup-test"
    
    # 创建目录创建函数测试
    cat > "$TEST_DIR/test-dir-creation.sh" << EOF
#!/bin/bash
localBackupPath="$test_backup_path"
nodeName="$TEST_NODE_NAME"
backupPermissions="755"

create_local_directories() {
    local base_path="\${localBackupPath}"
    local node_path="\${base_path}/\${nodeName}"
    local metadata_path="\${base_path}/metadata"
    local logs_path="\${base_path}/logs"
    
    mkdir -p "\${node_path}" "\${metadata_path}" "\${logs_path}"
    chmod "\${backupPermissions}" "\${base_path}" "\${node_path}"
    chmod 755 "\${metadata_path}" "\${logs_path}"
}

create_local_directories

# 验证目录是否创建成功
if [[ -d "$test_backup_path/$TEST_NODE_NAME" ]] && \
   [[ -d "$test_backup_path/metadata" ]] && \
   [[ -d "$test_backup_path/logs" ]]; then
    echo "PASS: All directories created successfully"
else
    echo "FAIL: Directory creation failed"
    exit 1
fi
EOF

    chmod +x "$TEST_DIR/test-dir-creation.sh"
    
    if "$TEST_DIR/test-dir-creation.sh" >/dev/null 2>&1; then
        test_result "Directory Creation" "PASS"
    else
        test_result "Directory Creation" "FAIL"
    fi
}

# 测试 4: 备份文件操作测试
test_backup_file_operations() {
    log_info "Running backup file operations test..."
    
    local backup_dir="$TEST_DIR/backup-ops"
    mkdir -p "$backup_dir"
    
    # 创建测试数据
    mkdir -p "$backup_dir/test-data"
    echo "test backup content" > "$backup_dir/test-data/test-file.txt"
    echo "another test file" > "$backup_dir/test-data/test-file2.txt"
    
    # 创建备份文件
    local backup_file="$backup_dir/test-backup.tar.gz"
    tar -czf "$backup_file" -C "$backup_dir" test-data
    
    # 验证备份文件
    if [[ -f "$backup_file" ]] && tar -tzf "$backup_file" >/dev/null 2>&1; then
        test_result "Backup File Operations" "PASS"
    else
        test_result "Backup File Operations" "FAIL"
    fi
}

# 测试 5: 备份保留策略测试
test_retention_policy() {
    log_info "Running retention policy test..."
    
    local retention_dir="$TEST_DIR/retention-test"
    mkdir -p "$retention_dir"
    
    # 创建多个模拟备份文件
    for i in {1..5}; do
        local backup_file="$retention_dir/2024-01-$(printf "%02d" $i)_10-30-00-backup.tar.gz"
        echo "backup content $i" > "$backup_file"
        # 设置不同的修改时间
        touch -t "2024010${i}1030" "$backup_file"
    done
    
    # 模拟保留策略（保留最新的3个文件）
    local retain_count=3
    local backup_files=($(ls -t "$retention_dir"/*.tar.gz 2>/dev/null))
    local file_count=${#backup_files[@]}
    local delete_count=$((file_count - retain_count))
    
    if [ $delete_count -gt 0 ]; then
        for ((i=retain_count; i<file_count; i++)); do
            rm -f "${backup_files[i]}"
        done
    fi
    
    # 验证保留策略
    local remaining_files=($(ls "$retention_dir"/*.tar.gz 2>/dev/null))
    local remaining_count=${#remaining_files[@]}
    
    if [ "$remaining_count" -eq "$retain_count" ]; then
        test_result "Retention Policy" "PASS"
    else
        test_result "Retention Policy" "FAIL"
        log_error "Expected $retain_count files, found $remaining_count"
    fi
}

# 测试 6: 完整性验证测试
test_integrity_validation() {
    log_info "Running integrity validation test..."
    
    local integrity_dir="$TEST_DIR/integrity-test"
    mkdir -p "$integrity_dir"
    
    # 创建有效的备份文件
    echo "valid backup content" > "$integrity_dir/valid-backup.tar.gz"
    tar -czf "$integrity_dir/valid-backup.tar.gz" -C "$TEST_DIR" mock-data
    
    # 创建无效的备份文件
    echo "invalid content" > "$integrity_dir/invalid-backup.tar.gz"
    
    # 测试有效文件验证
    if tar -tzf "$integrity_dir/valid-backup.tar.gz" >/dev/null 2>&1; then
        local valid_test="PASS"
    else
        local valid_test="FAIL"
    fi
    
    # 测试无效文件验证
    if ! tar -tzf "$integrity_dir/invalid-backup.tar.gz" >/dev/null 2>&1; then
        local invalid_test="PASS"
    else
        local invalid_test="FAIL"
    fi
    
    if [ "$valid_test" = "PASS" ] && [ "$invalid_test" = "PASS" ]; then
        test_result "Integrity Validation" "PASS"
    else
        test_result "Integrity Validation" "FAIL"
    fi
}

# 主测试函数
run_all_tests() {
    log_info "Starting backup-kube local storage tests..."
    
    # 设置测试环境
    setup_test_env
    
    # 运行所有测试
    test_parameter_parsing
    test_path_validation
    test_directory_creation
    test_backup_file_operations
    test_retention_policy
    test_integrity_validation
    
    # 输出测试结果
    echo
    log_info "Test Results Summary:"
    echo "  Total Tests: $TESTS_TOTAL"
    echo "  Passed: $TESTS_PASSED"
    echo "  Failed: $TESTS_FAILED"
    
    if [ $TESTS_FAILED -eq 0 ]; then
        log_info "All tests passed! ✅"
        return 0
    else
        log_error "$TESTS_FAILED test(s) failed! ❌"
        return 1
    fi
}

# 脚本入口点
main() {
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 运行测试
    if run_all_tests; then
        exit 0
    else
        exit 1
    fi
}

# 如果脚本被直接执行，运行主函数
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
