# Helm Chart 本地存储支持更新总结

## 📋 更新概览

为 backup-kube Helm Chart 添加了完整的本地存储支持，同时保持与现有 S3 配置的向后兼容性。

## 🔄 更新的文件

### 1. Chart.yaml
- **版本升级**: v1.0.2 → v1.1.0
- **应用版本**: v1.0.1 → v1.1.0
- **描述更新**: 添加了本地存储支持说明
- **新增元数据**: keywords, maintainers, home, sources

### 2. values.yaml
- **存储配置重构**: 新增 `storage` 配置节
- **本地存储选项**: 完整的本地存储配置参数
- **持久化配置**: 新增 `persistence` 配置节
- **向后兼容**: 保留原有 S3 配置参数
- **资源配置**: 新增资源限制和调度配置

### 3. templates/cronjob.yaml
- **参数动态化**: 根据存储类型动态生成命令参数
- **卷挂载支持**: 添加本地存储卷挂载逻辑
- **模板函数**: 使用 helper 函数提高可维护性
- **配置灵活化**: 支持更多 CronJob 配置选项

### 4. templates/_helpers.tpl (新增)
- **模板函数库**: 提供通用的模板函数
- **配置抽象**: 简化复杂配置的处理
- **向后兼容**: 处理新旧配置格式的兼容性

### 5. templates/pvc.yaml (新增)
- **持久化卷声明**: 为本地存储创建 PVC
- **条件创建**: 仅在启用本地存储和持久化时创建
- **灵活配置**: 支持自定义存储类和大小

### 6. templates/NOTES.txt
- **部署说明**: 提供详细的部署后使用指南
- **状态检查**: 包含监控和故障排除命令
- **配置总结**: 显示当前部署的关键配置信息

### 7. README.md
- **完整文档**: 详细的安装和配置说明
- **多语言支持**: 中英文混合文档
- **示例配置**: 提供多种使用场景的配置示例
- **故障排除**: 常见问题和解决方案

### 8. values-local-storage.yaml (新增)
- **配置示例**: 完整的本地存储配置示例
- **最佳实践**: 生产环境推荐配置
- **注释说明**: 详细的参数说明和使用建议

## 🚀 新功能特性

### 存储类型支持
- ✅ **S3 存储**: 保持原有功能不变
- ✅ **本地存储**: 新增本地文件系统支持
- ✅ **混合配置**: 支持在不同环境使用不同存储

### 本地存储选项
- ✅ **持久化卷**: 支持 PVC 持久化存储
- ✅ **主机路径**: 支持 hostPath 直接存储
- ✅ **压缩选项**: 可配置备份文件压缩
- ✅ **权限控制**: 自定义文件权限设置
- ✅ **保留策略**: 独立的本地备份保留配置

### 部署灵活性
- ✅ **资源管理**: CPU 和内存限制配置
- ✅ **调度控制**: 节点选择器和容忍度
- ✅ **亲和性**: Pod 反亲和性配置
- ✅ **安全上下文**: 容器安全配置

## 📊 配置对比

### 原有配置（v1.0.x）
```yaml
# 仅支持 S3
retainCount: 10
s3backupPrefix: "s3://kube-backup"
s3client:
  accessKey: "xxx"
  secretKey: "yyy"
  s3Host: "endpoint"
```

### 新配置（v1.1.x）
```yaml
# 支持多种存储类型
storage:
  type: "local"  # 或 "s3"
  local:
    path: "/backup"
    retainCount: 10
    compression: true
  s3:
    prefix: "s3://kube-backup"
    client:
      accessKey: "xxx"
      secretKey: "yyy"
      host: "endpoint"

# 持久化存储
persistence:
  enabled: true
  size: "100Gi"
```

## 🔄 升级指南

### 从 v1.0.x 升级到 v1.1.x

#### 1. 保持现有 S3 配置
```bash
# 直接升级，配置保持不变
helm upgrade backup-kube ./charts/backup-kube
```

#### 2. 迁移到新配置格式
```bash
# 使用新的配置格式
helm upgrade backup-kube ./charts/backup-kube \
  --set storage.type="s3" \
  --set storage.s3.client.accessKey="your-key" \
  --set storage.s3.client.secretKey="your-secret"
```

#### 3. 切换到本地存储
```bash
# 切换到本地存储
helm upgrade backup-kube ./charts/backup-kube \
  --set storage.type="local" \
  --set persistence.enabled=true \
  --set persistence.size="200Gi"
```

## 🧪 测试验证

### 1. 配置验证
```bash
# 验证 Chart 语法
helm lint ./charts/backup-kube

# 渲染模板检查
helm template backup-kube ./charts/backup-kube \
  --set storage.type="local" \
  --set persistence.enabled=true
```

### 2. 部署测试
```bash
# 测试部署（dry-run）
helm install backup-kube ./charts/backup-kube \
  --dry-run --debug \
  --set storage.type="local"

# 实际部署
helm install backup-kube ./charts/backup-kube \
  --set storage.type="local" \
  --set persistence.enabled=true
```

### 3. 功能验证
```bash
# 检查 CronJob 创建
kubectl get cronjob backup-kube

# 检查 PVC 创建（如果启用）
kubectl get pvc

# 手动触发备份任务
kubectl create job --from=cronjob/backup-kube manual-backup

# 查看备份日志
kubectl logs -l app.kubernetes.io/name=backup-kube
```

## 📝 注意事项

### 向后兼容性
- ✅ 现有 S3 配置完全兼容
- ✅ 原有参数名称保留
- ✅ 默认行为保持不变
- ⚠️ 建议逐步迁移到新配置格式

### 安全考虑
- 🔒 敏感信息使用 Secret 存储
- 🔒 文件权限合理设置
- 🔒 容器安全上下文配置
- 🔒 网络策略支持（可选）

### 性能优化
- ⚡ 资源限制合理配置
- ⚡ 压缩选项减少存储占用
- ⚡ 保留策略避免磁盘满
- ⚡ 调度策略优化性能

## 🎯 下一步计划

1. **监控集成**: 添加 Prometheus 监控支持
2. **告警配置**: 备份失败告警机制
3. **恢复工具**: 自动化恢复脚本
4. **多集群**: 跨集群备份支持
5. **加密支持**: 备份文件加密选项

---

**总结**: 本次更新为 backup-kube 添加了完整的本地存储支持，同时保持了良好的向后兼容性。用户可以根据需要选择合适的存储方案，并享受更灵活的配置选项。
