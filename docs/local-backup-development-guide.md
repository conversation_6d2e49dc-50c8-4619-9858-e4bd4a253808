# Kubernetes 备份项目本地存储支持开发手册

## 📋 项目概述

### 当前状态分析
- **项目名称**: backup-kube
- **当前功能**: 仅支持 S3 对象存储备份
- **备份内容**: K8s etcd快照、kubeverse etcd快照、证书配置文件
- **实现方式**: Bash 脚本 + Docker 容器化
- **部署方式**: Helm Chart 定时任务

### 目标需求
为项目增加本地文件系统备份支持，使用户可以选择将备份存储到：
1. 本地文件系统路径
2. 网络挂载存储（NFS、CIFS等）
3. 持久化卷（PV/PVC）

## 🏗️ 技术架构设计

### 存储抽象层设计
```
备份核心逻辑
    ↓
存储接口抽象层 (StorageInterface)
    ↓
┌─────────────┬─────────────────┐
│  S3Storage  │  LocalStorage   │
│  (现有)     │   (新增)        │
└─────────────┴─────────────────┘
```

### 配置参数扩展
```bash
# 新增本地存储相关参数
--storageType=local|s3           # 存储类型选择
--localBackupPath=/backup/path   # 本地备份路径
--localRetainCount=10            # 本地备份保留数量
--enableCompression=true         # 是否启用压缩
--backupPermissions=755          # 备份文件权限
```

## 📁 目录结构规划

### 本地备份目录结构
```
${localBackupPath}/
├── ${nodeName}/
│   ├── 2024-01-15_10-30-00-backup.tar.gz
│   ├── 2024-01-14_10-30-00-backup.tar.gz
│   └── ...
├── metadata/
│   ├── backup-index.json        # 备份索引文件
│   └── retention-policy.json    # 保留策略配置
└── logs/
    └── backup-operations.log    # 操作日志
```

### 备份文件内部结构保持不变
```
backup-cluster/
├── snapshot.db                 # etcd 快照
└── configs/
    ├── manifests/              # K8s 配置
    └── kubelet/               # kubelet 配置

backup-verse/
└── snapshot.db                # kubeverse etcd 快照
```

## 🔧 核心功能模块设计

### 1. 存储类型检测与初始化
```bash
function init_storage_backend() {
    case "${storageType}" in
        "local")
            init_local_storage
            ;;
        "s3")
            init_s3_config
            ;;
        *)
            exit_with_err "Unsupported storage type: ${storageType}"
            ;;
    esac
}
```

### 2. 本地存储初始化
```bash
function init_local_storage() {
    # 验证本地路径
    validate_local_path "${localBackupPath}"
    
    # 创建目录结构
    create_local_directories
    
    # 设置权限
    setup_local_permissions
    
    # 初始化元数据
    init_backup_metadata
}
```

### 3. 本地备份上传
```bash
function post_backup_to_local() {
    local backupFileName="${date}-backup.tar.gz"
    local targetPath="${localBackupPath}/${nodeName}/${backupFileName}"
    
    # 创建压缩包
    tar -czf "${backupFileName}" "${kubeBackupRootNowDir}"
    
    # 移动到目标位置
    mv "${backupFileName}" "${targetPath}"
    
    # 更新元数据
    update_backup_metadata "${targetPath}"
    
    # 验证备份完整性
    validate_backup_integrity "${targetPath}"
}
```

### 4. 本地备份清理
```bash
function clear_old_local_backup() {
    local backupDir="${localBackupPath}/${nodeName}"
    
    # 获取备份文件列表（按时间排序）
    local backupFiles=($(ls -t "${backupDir}"/*.tar.gz 2>/dev/null))
    local fileCount=${#backupFiles[@]}
    
    # 计算需要删除的文件数量
    local deleteCount=$((fileCount - localRetainCount))
    
    if [ $deleteCount -gt 0 ]; then
        # 删除最旧的备份文件
        for ((i=localRetainCount; i<fileCount; i++)); do
            rm -f "${backupFiles[i]}"
            log_operation "Deleted old backup: ${backupFiles[i]}"
        done
    fi
}
```

## 📝 实施计划

### 阶段一：核心功能开发 (预计 3-5 天)
1. **参数解析扩展** (0.5天)
   - 添加本地存储相关参数
   - 更新参数验证逻辑

2. **存储抽象层实现** (1天)
   - 设计存储接口
   - 重构现有 S3 逻辑

3. **本地存储核心功能** (2天)
   - 本地目录管理
   - 备份文件操作
   - 元数据管理

4. **备份保留策略** (1天)
   - 本地文件清理逻辑
   - 备份完整性验证

5. **错误处理与日志** (0.5天)
   - 完善错误处理
   - 添加操作日志

### 阶段二：测试与优化 (预计 2-3 天)
1. **单元测试** (1天)
   - 核心函数测试
   - 边界条件测试

2. **集成测试** (1天)
   - 完整备份流程测试
   - 多存储类型切换测试

3. **性能优化** (1天)
   - 大文件处理优化
   - 并发安全性验证

### 阶段三：文档与部署 (预计 1-2 天)
1. **文档更新** (0.5天)
   - README 更新
   - 配置说明文档

2. **容器镜像构建** (0.5天)
   - Dockerfile 更新
   - 多架构构建测试

3. **Helm Chart 更新** (1天)
   - 配置模板更新
   - 默认值设置

## 🔍 关键技术要点

### 1. 路径安全性
- 验证备份路径的合法性
- 防止路径遍历攻击
- 确保有足够的磁盘空间

### 2. 原子性操作
- 使用临时文件避免备份过程中的数据损坏
- 备份完成后再移动到最终位置

### 3. 权限管理
- 合理设置文件和目录权限
- 支持自定义权限配置

### 4. 错误恢复
- 备份失败时的清理机制
- 部分备份的处理策略

## 🚀 兼容性考虑

### 向后兼容
- 保持现有 S3 备份功能不变
- 默认存储类型为 S3，确保现有部署不受影响

### 配置迁移
- 提供配置迁移工具
- 支持混合存储模式（同时备份到 S3 和本地）

## 📊 验收标准

### 功能验收
- [ ] 支持本地文件系统备份
- [ ] 支持备份保留策略
- [ ] 支持备份完整性验证
- [ ] 支持存储类型动态切换
- [ ] 保持现有 S3 功能完整性

### 性能验收
- [ ] 备份时间不超过现有方案的 120%
- [ ] 支持 GB 级别的备份文件
- [ ] 内存使用稳定，无内存泄漏

### 安全验收
- [ ] 备份文件权限正确设置
- [ ] 路径验证防止安全漏洞
- [ ] 敏感信息不在日志中泄露

## 🛠️ 开发环境准备

### 必需工具
- Docker & Docker Buildx
- Kubernetes 集群（用于测试）
- etcdctl 工具
- tar、gzip 等基础工具

### 测试数据准备
- 模拟 etcd 快照文件
- 测试用的 K8s 配置文件
- 不同大小的备份数据集

## 💻 详细代码实现示例

### 1. 参数解析扩展
```bash
# 在现有参数基础上添加
storageType="s3"  # 默认保持 S3
localBackupPath="/backup"
localRetainCount=10
enableCompression="true"
backupPermissions="755"

# 参数解析部分添加
case $arg in
    --storageType=*)
        storageType="${arg#*=}"
        shift
        ;;
    --localBackupPath=*)
        localBackupPath="${arg#*=}"
        shift
        ;;
    --localRetainCount=*)
        localRetainCount="${arg#*=}"
        shift
        ;;
    --enableCompression=*)
        enableCompression="${arg#*=}"
        shift
        ;;
    --backupPermissions=*)
        backupPermissions="${arg#*=}"
        shift
        ;;
esac
```

### 2. 存储后端工厂模式
```bash
function create_storage_backend() {
    case "${storageType}" in
        "local")
            echo "LocalStorage"
            ;;
        "s3")
            echo "S3Storage"
            ;;
        *)
            exit_with_err "Unknown storage type: ${storageType}"
            ;;
    esac
}

function execute_storage_operation() {
    local operation=$1
    local backend=$(create_storage_backend)

    case "${backend}" in
        "LocalStorage")
            case "${operation}" in
                "init") init_local_storage ;;
                "upload") post_backup_to_local ;;
                "cleanup") clear_old_local_backup ;;
            esac
            ;;
        "S3Storage")
            case "${operation}" in
                "init") init_s3_config ;;
                "upload") post_backup_to_s3 ;;
                "cleanup") clear_old_backup ;;
            esac
            ;;
    esac
}
```

### 3. 本地存储完整实现
```bash
function validate_local_path() {
    local path=$1

    # 检查路径是否为绝对路径
    if [[ ! "$path" = /* ]]; then
        exit_with_err "Local backup path must be absolute: $path"
    fi

    # 检查父目录是否存在且可写
    local parent_dir=$(dirname "$path")
    if [[ ! -d "$parent_dir" ]]; then
        exit_with_err "Parent directory does not exist: $parent_dir"
    fi

    if [[ ! -w "$parent_dir" ]]; then
        exit_with_err "Parent directory is not writable: $parent_dir"
    fi

    # 检查磁盘空间（至少需要 5GB）
    local available_space=$(df "$parent_dir" | awk 'NR==2 {print $4}')
    local required_space=5242880  # 5GB in KB

    if [[ $available_space -lt $required_space ]]; then
        exit_with_err "Insufficient disk space. Required: 5GB, Available: $((available_space/1024/1024))GB"
    fi
}

function create_local_directories() {
    local base_path="${localBackupPath}"
    local node_path="${base_path}/${nodeName}"
    local metadata_path="${base_path}/metadata"
    local logs_path="${base_path}/logs"

    mkdir -p "${node_path}" "${metadata_path}" "${logs_path}"

    # 设置目录权限
    chmod "${backupPermissions}" "${base_path}"
    chmod "${backupPermissions}" "${node_path}"
    chmod 755 "${metadata_path}" "${logs_path}"
}

function init_backup_metadata() {
    local metadata_file="${localBackupPath}/metadata/backup-index.json"

    if [[ ! -f "$metadata_file" ]]; then
        cat > "$metadata_file" << EOF
{
    "version": "1.0",
    "created": "$(date -Iseconds)",
    "node": "${nodeName}",
    "backups": []
}
EOF
    fi
}

function update_backup_metadata() {
    local backup_file=$1
    local metadata_file="${localBackupPath}/metadata/backup-index.json"
    local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file")
    local file_hash=$(sha256sum "$backup_file" | cut -d' ' -f1)

    # 创建临时文件更新元数据
    local temp_file=$(mktemp)
    jq --arg file "$backup_file" \
       --arg size "$file_size" \
       --arg hash "$file_hash" \
       --arg timestamp "$(date -Iseconds)" \
       '.backups += [{
           "file": $file,
           "size": ($size | tonumber),
           "hash": $hash,
           "timestamp": $timestamp
       }]' "$metadata_file" > "$temp_file"

    mv "$temp_file" "$metadata_file"
}

function validate_backup_integrity() {
    local backup_file=$1

    # 检查文件是否存在
    if [[ ! -f "$backup_file" ]]; then
        exit_with_err "Backup file not found: $backup_file"
    fi

    # 检查文件大小
    local file_size=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file")
    if [[ $file_size -eq 0 ]]; then
        exit_with_err "Backup file is empty: $backup_file"
    fi

    # 验证 tar 文件完整性
    if ! tar -tzf "$backup_file" >/dev/null 2>&1; then
        exit_with_err "Backup file is corrupted: $backup_file"
    fi

    log_operation "Backup integrity validated: $backup_file (${file_size} bytes)"
}

function log_operation() {
    local message=$1
    local log_file="${localBackupPath}/logs/backup-operations.log"
    local timestamp=$(date -Iseconds)

    echo "[$timestamp] [${nodeName}] $message" >> "$log_file"
    echo "$message"  # 同时输出到控制台
}
```

## 🔧 配置文件模板

### Helm Values 扩展
```yaml
# values.yaml 新增配置项
backup:
  # 存储类型配置
  storage:
    type: "s3"  # s3 | local

  # S3 配置（保持现有）
  s3:
    endpoint: ""
    accessKey: ""
    secretKey: ""
    bucket: "kube-backup"
    region: "us-east-1"

  # 本地存储配置（新增）
  local:
    path: "/backup"
    retainCount: 10
    compression: true
    permissions: "755"

  # 通用配置
  schedule: "0 0 * * *"  # 每天凌晨执行
  retainCount: 10

# 持久化卷配置（用于本地存储）
persistence:
  enabled: false
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 100Gi
  mountPath: /backup
```

### ConfigMap 模板
```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: backup-config
data:
  backup.conf: |
    STORAGE_TYPE={{ .Values.backup.storage.type }}
    {{- if eq .Values.backup.storage.type "local" }}
    LOCAL_BACKUP_PATH={{ .Values.backup.local.path }}
    LOCAL_RETAIN_COUNT={{ .Values.backup.local.retainCount }}
    ENABLE_COMPRESSION={{ .Values.backup.local.compression }}
    BACKUP_PERMISSIONS={{ .Values.backup.local.permissions }}
    {{- else }}
    S3_BACKUP_PREFIX={{ .Values.backup.s3.bucket }}
    S3_HOST={{ .Values.backup.s3.endpoint }}
    S3_REGION={{ .Values.backup.s3.region }}
    {{- end }}
    RETAIN_COUNT={{ .Values.backup.retainCount }}
```

## 🧪 测试用例设计

### 单元测试脚本
```bash
#!/bin/bash
# test-local-backup.sh

source ./kube-backup.sh

# 测试用例 1: 路径验证
test_validate_local_path() {
    echo "Testing path validation..."

    # 测试相对路径（应该失败）
    if validate_local_path "relative/path" 2>/dev/null; then
        echo "FAIL: Should reject relative path"
        return 1
    fi

    # 测试不存在的路径（应该失败）
    if validate_local_path "/nonexistent/path" 2>/dev/null; then
        echo "FAIL: Should reject nonexistent path"
        return 1
    fi

    echo "PASS: Path validation works correctly"
    return 0
}

# 测试用例 2: 目录创建
test_create_local_directories() {
    echo "Testing directory creation..."

    local test_path="/tmp/backup-test-$$"
    localBackupPath="$test_path"
    nodeName="test-node"
    backupPermissions="755"

    create_local_directories

    if [[ ! -d "$test_path/test-node" ]]; then
        echo "FAIL: Node directory not created"
        return 1
    fi

    if [[ ! -d "$test_path/metadata" ]]; then
        echo "FAIL: Metadata directory not created"
        return 1
    fi

    # 清理
    rm -rf "$test_path"
    echo "PASS: Directory creation works correctly"
    return 0
}

# 运行所有测试
run_tests() {
    local failed=0

    test_validate_local_path || ((failed++))
    test_create_local_directories || ((failed++))

    if [[ $failed -eq 0 ]]; then
        echo "All tests passed!"
        exit 0
    else
        echo "$failed tests failed!"
        exit 1
    fi
}

run_tests
```

## 📋 部署检查清单

### 开发阶段检查
- [ ] 代码符合现有项目风格
- [ ] 所有新增函数都有错误处理
- [ ] 日志记录完整且格式统一
- [ ] 参数验证覆盖所有边界情况
- [ ] 向后兼容性测试通过

### 测试阶段检查
- [ ] 单元测试覆盖率 > 80%
- [ ] 集成测试在真实 K8s 环境通过
- [ ] 性能测试满足要求
- [ ] 安全扫描无高危漏洞
- [ ] 多架构构建成功

### 部署阶段检查
- [ ] Helm Chart 模板语法正确
- [ ] 默认配置值合理
- [ ] 文档更新完整
- [ ] 版本号正确递增
- [ ] 镜像推送到正确仓库

---

**注意**: 本手册为技术实施指导，具体开发时需要根据实际环境和需求进行调整。建议在开发过程中保持与运维团队的密切沟通，确保方案的可操作性和稳定性。
