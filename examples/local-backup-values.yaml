# Helm Values 配置示例 - 本地备份支持
# 此文件展示了如何配置 backup-kube 使用本地存储

# 全局配置
global:
  imageRegistry: "registry.bingosoft.net"
  imagePullSecrets: []

# 备份配置
backup:
  # 镜像配置
  image:
    repository: "bingokube/backup-kube"
    tag: "v1.1.0-local"
    pullPolicy: IfNotPresent

  # 存储配置 - 选择存储类型
  storage:
    # 存储类型: "s3" 或 "local"
    type: "local"
    
  # S3 存储配置（当 storage.type = "s3" 时使用）
  s3:
    endpoint: "s3.amazonaws.com"
    accessKey: ""
    secretKey: ""
    bucket: "kube-backup"
    region: "us-east-1"
    # S3 备份路径前缀
    prefix: "s3://kube-backup"
    
  # 本地存储配置（当 storage.type = "local" 时使用）
  local:
    # 本地备份路径（容器内路径）
    path: "/backup"
    # 本地备份保留数量
    retainCount: 10
    # 是否启用压缩
    compression: true
    # 备份文件权限
    permissions: "755"
    
  # 通用备份配置
  # 备份保留数量（向后兼容，优先使用具体存储类型的配置）
  retainCount: 10
  
  # 调度配置
  schedule: "0 2 * * *"  # 每天凌晨2点执行
  
  # 节点配置
  nodeSelector: {}
  tolerations: []
  affinity: {}
  
  # 资源限制
  resources:
    limits:
      cpu: 500m
      memory: 512Mi
    requests:
      cpu: 100m
      memory: 128Mi
      
  # 安全上下文
  securityContext:
    runAsUser: 0
    runAsGroup: 0
    fsGroup: 0
    
  # Kubernetes 集群配置
  cluster:
    # 是否备份 kubeverse etcd
    kubeVerse: false
    # 是否启用 TLS
    kubeTls: true

# 持久化存储配置（用于本地备份）
persistence:
  # 是否启用持久化存储
  enabled: true
  
  # 存储类名称（留空使用默认存储类）
  storageClass: ""
  
  # 访问模式
  accessMode: ReadWriteOnce
  
  # 存储大小
  size: 100Gi
  
  # 挂载路径（应与 backup.local.path 一致）
  mountPath: /backup
  
  # 是否保留 PVC（删除 Helm release 时）
  retain: true
  
  # 存储配置注解
  annotations: {}
  
  # 现有 PVC 名称（如果要使用现有 PVC）
  existingClaim: ""

# ServiceAccount 配置
serviceAccount:
  # 是否创建 ServiceAccount
  create: true
  # ServiceAccount 名称
  name: ""
  # ServiceAccount 注解
  annotations: {}

# RBAC 配置
rbac:
  # 是否创建 RBAC 资源
  create: true

# ConfigMap 配置
configMap:
  # 是否创建 ConfigMap
  create: true
  # ConfigMap 名称
  name: ""
  # 额外的配置项
  data: {}

# Secret 配置
secret:
  # 是否创建 Secret
  create: true
  # Secret 名称
  name: ""
  # S3 凭据（当使用 S3 存储时）
  s3:
    accessKey: ""
    secretKey: ""

# CronJob 配置
cronjob:
  # 是否启用 CronJob
  enabled: true
  
  # 并发策略
  concurrencyPolicy: Forbid
  
  # 失败任务历史保留数量
  failedJobsHistoryLimit: 3
  
  # 成功任务历史保留数量
  successfulJobsHistoryLimit: 3
  
  # 任务重启策略
  restartPolicy: OnFailure
  
  # 任务超时时间（秒）
  activeDeadlineSeconds: 3600
  
  # 任务回溯限制
  startingDeadlineSeconds: 300

# 监控配置
monitoring:
  # 是否启用监控
  enabled: false
  
  # ServiceMonitor 配置
  serviceMonitor:
    enabled: false
    namespace: ""
    labels: {}
    interval: 30s
    
  # 告警规则
  prometheusRule:
    enabled: false
    namespace: ""
    labels: {}

# 网络策略
networkPolicy:
  # 是否启用网络策略
  enabled: false
  
  # 入站规则
  ingress: []
  
  # 出站规则
  egress: []

---
# 使用示例配置

# 示例 1: 基础本地备份配置
basic_local_backup: &basic_local
  backup:
    storage:
      type: "local"
    local:
      path: "/backup"
      retainCount: 7
    schedule: "0 1 * * *"
  persistence:
    enabled: true
    size: 50Gi

# 示例 2: 高可用本地备份配置
ha_local_backup: &ha_local
  backup:
    storage:
      type: "local"
    local:
      path: "/backup"
      retainCount: 30
      compression: true
    schedule: "0 2 * * *"
    resources:
      limits:
        cpu: 1000m
        memory: 1Gi
      requests:
        cpu: 200m
        memory: 256Mi
  persistence:
    enabled: true
    size: 200Gi
    storageClass: "fast-ssd"
  monitoring:
    enabled: true

# 示例 3: 混合存储配置（需要自定义实现）
# hybrid_backup: &hybrid
#   backup:
#     storage:
#       type: "hybrid"  # 同时备份到本地和 S3
#     local:
#       path: "/backup"
#       retainCount: 7
#     s3:
#       bucket: "kube-backup"
#       retainCount: 30

# 示例 4: 开发环境配置
dev_environment: &dev
  backup:
    storage:
      type: "local"
    local:
      path: "/backup"
      retainCount: 3
    schedule: "0 */6 * * *"  # 每6小时备份一次
    cluster:
      kubeVerse: false
  persistence:
    enabled: true
    size: 10Gi
  resources:
    limits:
      cpu: 200m
      memory: 256Mi
    requests:
      cpu: 50m
      memory: 64Mi

# 示例 5: 生产环境配置
production_environment: &prod
  backup:
    storage:
      type: "local"
    local:
      path: "/backup"
      retainCount: 30
      compression: true
      permissions: "750"
    schedule: "0 2 * * *"
    cluster:
      kubeVerse: true
      kubeTls: true
    nodeSelector:
      node-role.kubernetes.io/master: ""
    tolerations:
    - key: node-role.kubernetes.io/master
      operator: Exists
      effect: NoSchedule
  persistence:
    enabled: true
    size: 500Gi
    storageClass: "backup-storage"
    retain: true
  monitoring:
    enabled: true
  resources:
    limits:
      cpu: 2000m
      memory: 2Gi
    requests:
      cpu: 500m
      memory: 512Mi
