#!/bin/bash

nodeName=""
kubeVerse="false"
retainCount=10
s3backupPrefix="s3://kube-backup"
accessKey=""
secretKey=""
s3Host=""
region=""
nodeName="$(echo $NODE_NAME)"
hostIp="$( echo $HOST_IP)"
kubeTls="true"

# get args
for arg in $@
do
	case $arg in
    #当前节点名称
		--nodeName=*)
		nodeName="${arg#*=}"
		shift
		;;
    #是否是kubeVerse控制集群，默认false
		--kubeVerse=*)
		kubeVerse="${arg#*=}"
		shift
		;;
    #备份保留数量，默认10份
		--retainCount=*)
		retainCount="${arg#*=}"
		shift
		;;
    #s3备份路径前缀
		--s3backupPrefix=*)
		s3backupPrefix="${arg#*=}"
		shift
		;;
    #accessKey
		--accessKey=*)
		accessKey="${arg#*=}"
		shift
		;;
    #secretKey
		--secretKey=*)
		secretKey="${arg#*=}"
		shift
		;;
    #s3地址
		--s3Host=*)
		s3Host="${arg#*=}"
		shift
		;;
    #区域
		--region=*)
		region="${arg#*=}"
		shift
		;;
	  --kubeTls=*)
		kubeTls="${arg#*=}"
		shift
		;;
		*)
		;;
	esac
done

# 获取当前时区
current_timezone=$(date +%Z)

# 检查当前时区并进行相应处理
if [[ "$current_timezone" == "UTC" ]]; then
    # 获取当前的 UTC 时间（以秒为单位）
    utc_timestamp=$(date -u +"%s")

    # 将 UTC 时间加上 8 小时的秒数（CST）
    cst_timestamp=$((utc_timestamp + 8 * 3600))

    # 将 CST 时间格式化为可读的日期格式
    current_time=$(date -d "@$cst_timestamp" +"%Y-%m-%d-%H:%M:%S")
else
    current_time=$(date +"%Y-%m-%d-%H:%M:%S")
fi

#全局变量
date=$current_time
kubeBackupRootDir="/bingokube/backup/${nodeName}/"
kubeBackupRootNowDir="${kubeBackupRootDir}${date}"
kubeClusterBackupDir="${kubeBackupRootNowDir}/backup-cluster"
kubeVerseBackupDir="${kubeBackupRootNowDir}/backup-verse"
backupClusterConfigDir="${kubeClusterBackupDir}/configs/"
backupConfigFile="${kubeBackupRootDir}${date}-${nodeName}-config.tar.gz"
# 创建备份目录
function create_dir() {
    mkdir -p "${kubeBackupRootNowDir}"
    mkdir -p "${kubeClusterBackupDir}"
    mkdir -p "${kubeVerseBackupDir}"
    mkdir -p "${backupClusterConfigDir}"
}
# 错误退出
function exit_with_err() {
  echo "$1"
  exit 1
}
# 备份集群etcd数据
function backup_cluster_etcd() {
    echo "Start to backup cluster etcd snapshot..."
    backupEtcdFile="${kubeClusterBackupDir}/snapshot.db"
    etcdctl --endpoints=https://${hostIp}:2379 \
        --cacert=/etc/kubernetes/pki/etcd/ca.crt \
        --cert=/etc/kubernetes/pki/etcd/healthcheck-client.crt \
        --key=/etc/kubernetes/pki/etcd/healthcheck-client.key \
        snapshot save "${backupEtcdFile}";
    if [ $? != 0 ]; then
        exit_with_err "Failed to backup cluster etcd"
    fi
    echo "Validating cluster etcd  snapshot..."
    # 快照有效性检查
    etcdctl snapshot status "${backupEtcdFile}"
    if [ $? != 0 ]; then
      exit_with_err "Failed to validate cluster etcd snapshot"
    fi
    echo "Success backup cluster etcd snapshot"
}

function backup_verse_etcd() {
    echo "Start to backup kubeverse etcd snapshot..."
   backupEtcdFile="${kubeVerseBackupDir}/snapshot.db"
   isKubeTls=$( echo "${kubeTls}" | sed  's/\"//g' )
   if [ $isKubeTls = "true" ]; then
      etcdctl --endpoints=https://etcd-cluster-headless.kube-system.svc.cluster.local:2379 \
                 --cacert=/home/<USER>/etcd-ca.pem \
                 --cert=/home/<USER>/etcd.pem \
                 --key=/home/<USER>/etcd-key.pem \
            snapshot save "${backupEtcdFile}";
   else
     etcdctl --endpoints=http://etcd-cluster-headless.kube-system:2379 \
                snapshot save "${backupEtcdFile}";
   fi
    if [ $? != 0 ]; then
        exit_with_err "Failed to backup kubeverse etcd"
    fi
    echo "Validating kubeverse etcd snapshot..."
    # 快照有效性检查
    etcdctl snapshot status "${backupEtcdFile}"
    if [ $? != 0 ]; then
      exit_with_err "Failed to validate kubeverse etcd snapshot"
    fi
    echo "Success backup kubeverse etcd snapshot"
}

# 备份证书和静态pod等
function backup_k8s() {
    echo "Start backup cluster manifests and pki..."
    manifestsDir="${backupClusterConfigDir}/manifests/"
    kubeletDir="${backupClusterConfigDir}/kubelet/"
    mkdir -p "${manifestsDir}"
    mkdir -p "${kubeletDir}"
    # 备份集群证书及配置文件
    cp /var/lib/kubelet/config.yaml "${kubeletDir}"
    cp /var/lib/kubelet/kubeadm-flags.env "${kubeletDir}"
    cp -r  /var/lib/kubelet/pki/ "${kubeletDir}"
    cp -r /etc/kubernetes/ "${manifestsDir}"
    tar -czf "${backupConfigFile}"  "${backupClusterConfigDir}"
    echo "Success backup cluster manifests and pki"
}

function init_s3_config() {
    sed -i "s/^secret_key.*/secret_key = ${secretKey}/" /root/.s3cfg
    sed -i "s/^access_key.*/access_key = ${accessKey}/" /root/.s3cfg
    sed -i "s/^host_base.*/host_base = ${s3Host}/" /root/.s3cfg
}

# 推送备份到s3
function post_backup_to_s3() {
    backupFileName="${date}-backup.tar.gz"
    tar -czf "${backupFileName}" "${kubeBackupRootNowDir}"
    if [ $? != 0 ]; then
         exit_with_err "Failed to post backup file to s3cmd"
    fi
    prefix=$( echo "${s3backupPrefix}" | sed  's/\"//g' )
    upFile=$( echo "${s3backupPrefix}/${nodeName}/${backupFileName}" | sed  's/\"//g' )
    s3cmd mb "${prefix}"
    echo "s3cmd put ${backupFileName} ${upFile}"
    s3cmd put "/app/${backupFileName}" "${upFile}"
}


function clear_old_backup() {
   # 列出 S3 存储桶中的所有对象，并按修改时间排序
   echo "s3cmd ls ${s3backupPrefix}/${nodeName}/ | sort -k1,1"
   s3backupBucket=$( echo "${s3backupPrefix}/${nodeName}/" | sed  's/\"//g' )
   echo "s3cmd ls ${s3backupBucket} | sort -k1,1"
   objects=$(s3cmd ls ${s3backupBucket} | sort -k1,1)
   echo "${objects}"
   # 获取对象的数量
   objectCount=$(echo "${objects}" | wc -l)
   echo "already had count : ${objectCount}"
   rc=$( echo "${retainCount}" | sed  's/\"//g' )
   # 计算删除份数
   deleteCount=$((objectCount - rc))
   echo "need tp delete count : ${deleteCount}"
   # 如果有超过，则删除多余的文件
   if [ $deleteCount -gt 0 ]; then
       objects_to_delete=$(echo "$objects" | head -n $deleteCount | awk '{print $4}')
       for object in $objects_to_delete; do
           echo "Deleting: $object"
           s3cmd del "$object"
       done
   else
       echo "No need to delete."
   fi
}



# 主程序
create_dir
backup_cluster_etcd

isBackUpKubeVerse=$( echo "${kubeVerse}" | sed  's/\"//g' )
if [ $isBackUpKubeVerse = "true" ]; then
	backup_verse_etcd
fi
backup_k8s
source /path/to/venv/bin/activate
init_s3_config
post_backup_to_s3
clear_old_backup








